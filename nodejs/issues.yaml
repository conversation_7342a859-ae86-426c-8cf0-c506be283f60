issues:
  - title: "[POC] Signadot sandboxes for PRs on EKS"
    description: |
      # Background
      Signadot provides request-routed microservice sandboxes so we can preview changes without cloning every dependency.

      # Tasks
      - Install Signadot operator on non-prod EKS
      - Configure sandbox routing for 2 representative services (e.g., web→api→search-service)
      - Wire PR triggers from GitHub Actions to create sandboxes; share preview URLs
      - Validate traffic isolation and compatibility with shared RDS/SQS
      - Measure cost/perf compared to full-env clones

      # Acceptance Criteria
      - PR creates sandbox in <5 minutes and routes only PR traffic to changed services
      - Works with shared SQS/RDS and our NestJS/Next.js stack
      - Automated teardown on PR close

    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "a0dae581-dd76-40cd-8f20-e93dfae4cb1c"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] Bunnyshell PR environments"
    description: |
      # Background
      Bunnyshell automates ephemeral/preview environments per PR.

      # Tasks
      - Connect GitHub repo(s) and EKS
      - Define environment template incl. 3+ services and an external RDS + SQS
      - Enable auto-create on PR; publish preview URL; validate teardown
      - Document developer workflow and required secrets

      # Acceptance Criteria
      - PR spins an environment with all services and reachable RDS/SQS
      - Auto cleanup on merge/close
      - One-pager on onboarding steps for engineers

    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "a0dae581-dd76-40cd-8f20-e93dfae4cb1c"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] Humanitec + Score for ephemeral envs"
    description: |
      # Background
      Humanitec Platform Orchestrator reads score.yaml to provision workloads and AWS resources per env (e.g., SQS, RDS).

      # Tasks
      - Define score.yaml for a 3-service app
      - Configure AWS resource packs (SQS, RDS/Aurora)
      - Wire PR pipelines to create ephemeral envs; test tear-down

      # Acceptance Criteria
      - PR creates an env + required SQS queue(s) + DB instance/clone
      - Engineers access preview URL and logs
      - Automated teardown removes cloud resources

    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "a0dae581-dd76-40cd-8f20-e93dfae4cb1c"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] Release Environments-as-a-Service"
    description: |
      # Background
      Release provides managed on-demand, production-like environments via AWS Marketplace. It can provision complex multi-service apps with AWS dependencies like RDS and SQS.

      # Tasks
      - Provision a Release account and connect to AWS/EKS.
      - Define environment templates with at least 3 interdependent services.
      - Integrate with GitHub to create environments per PR.
      - Validate RDS/SQS connectivity from deployed services.
      - Test automated cleanup on PR close.

      # Acceptance Criteria
      - PR spins a fully functional multi-service environment with AWS service integration.
      - Preview URL accessible and shareable.
      - Automated teardown and resource cleanup on PR close.
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "f6c730b2-f162-4467-8cef-8c4385dbafdc"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] Qovery Preview Environments on AWS/EKS"
    description: |
      # Background
      Qovery automates ephemeral and preview environments on AWS/EKS. Supports external dependencies like RDS and SQS and integrates with TypeScript/NestJS/NextJS apps.

      # Tasks
      - Connect GitHub repos and EKS to Qovery.
      - Create a template for a representative multi-service app with AWS dependencies.
      - Enable auto-create of preview environments on PR.
      - Test teardown and cost metrics.

      # Acceptance Criteria
      - PR triggers a Qovery-managed environment in <10 min.
      - Services can connect to RDS/SQS and other internal APIs.
      - Environments auto-delete after merge/close.
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "f6c730b2-f162-4467-8cef-8c4385dbafdc"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] Garden for Ephemeral and Preview Environments"
    description: |
      # Background
      Garden provides automation for building, testing, and deploying apps to Kubernetes with preview environments. It can orchestrate multi-service deployments with AWS integrations.

      # Tasks
      - Install and configure Garden on a non-prod EKS cluster.
      - Define a Garden project for a multi-service app with dependencies.
      - Integrate with CI/CD to create environments per PR.
      - Validate AWS service integration (SQS, RDS).
      - Measure deployment time and developer experience.

      # Acceptance Criteria
      - PR deploys preview environments automatically via Garden.
      - All services reachable with inter-service calls functioning.
      - AWS dependencies working as expected.
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "f6c730b2-f162-4467-8cef-8c4385dbafdc"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] vCluster Platform for Isolated PR Environments"
    description: |
      # Background
      vCluster Platform creates virtual Kubernetes clusters for isolation, reducing conflicts between PR environments while sharing compute.

      # Tasks
      - Deploy vCluster Platform in non-prod AWS EKS.
      - Configure GitHub Actions to spin a vCluster per PR.
      - Deploy 3+ interdependent services into each vCluster.
      - Validate AWS service connectivity and inter-service dependencies.
      - Implement teardown workflow.

      # Acceptance Criteria
      - vCluster is created for each PR in <5 min.
      - All services deploy successfully and can connect to AWS services.
      - vCluster deleted automatically on PR close.
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "f6c730b2-f162-4467-8cef-8c4385dbafdc"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

  - title: "[POC] Uffizzi Ephemeral Previews"
    description: |
      # Background
      Uffizzi offers ephemeral preview environments for Kubernetes or Docker Compose-based deployments. It supports open-source and SaaS workflows.

      # Tasks
      - Set up Uffizzi controller in non-prod EKS or use Uffizzi SaaS.
      - Configure a representative multi-service deployment with AWS dependencies.
      - Enable PR-triggered environment creation.
      - Validate performance and AWS integration.

      # Acceptance Criteria
      - PR deploys to an ephemeral environment via Uffizzi.
      - Multi-service communication and AWS services function correctly.
      - Automated cleanup occurs on PR close.
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "f6c730b2-f162-4467-8cef-8c4385dbafdc"
    milestoneId: "a46c435d-0fe7-4b6d-81d9-526b69519c6c"

